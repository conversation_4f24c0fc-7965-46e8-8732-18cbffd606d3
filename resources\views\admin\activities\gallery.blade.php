@extends('layouts.admin')

@section('title', 'จัดการแกลเลอรี่ - ' . $activity->title)

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-images me-2 text-info"></i>จัดการแกลเลอรี่
                    </h1>
                    <p class="text-muted">จัดการรูปภาพแกลเลอรี่: {{ $activity->title }}</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.dashboard') }}">
                                <i class="fas fa-home"></i> แดชบอร์ด
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.activities.index') }}">
                                <i class="fas fa-calendar-alt"></i> จัดการกิจกรรม
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.activities.edit', $activity) }}">
                                <i class="fas fa-edit"></i> แก้ไขกิจกรรม
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            <i class="fas fa-images"></i> จัดการแกลเลอรี่
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Activity Info -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-body py-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="mb-1">{{ $activity->title }}</h5>
                                    <small class="text-muted">{{ $activity->images->count() }} รูปภาพในแกลเลอรี่</small>
                                </div>
                                <div>
                                    <a href="{{ route('admin.activities.edit', $activity) }}" class="btn btn-secondary btn-sm">
                                        <i class="fas fa-arrow-left"></i> กลับไปแก้ไขกิจกรรม
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Upload New Images -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-upload"></i> เพิ่มรูปภาพใหม่
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('admin.activities.gallery.upload', $activity) }}" method="POST" enctype="multipart/form-data" id="uploadForm">
                                @csrf
                                
                                <div class="mb-3">
                                    <label for="gallery_images" class="form-label">เลือกรูปภาพ</label>
                                    <input type="file" class="form-control @error('gallery_images') is-invalid @enderror"
                                           id="gallery_images" name="gallery_images[]" accept="image/*" multiple>
                                    <small class="form-text text-muted">สามารถเลือกหลายไฟล์พร้อมกัน (JPG, PNG, GIF - ขนาดไม่เกิน 2MB ต่อไฟล์)</small>
                                    @error('gallery_images')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div id="imagePreview" class="row mb-3">
                                    <!-- Preview images will appear here -->
                                </div>

                                <div id="uploadHelp" class="text-center text-muted mb-3">
                                    <i class="fas fa-images fa-2x mb-2"></i>
                                    <p class="mb-0">เลือกรูปภาพเพื่อดู preview ก่อนอัพโหลด</p>
                                </div>

                                <div class="text-center">
                                    <button type="submit" class="btn btn-success" id="uploadBtn" style="display: none;">
                                        <i class="fas fa-upload"></i> อัพโหลดรูปภาพ (<span id="fileCount">0</span> ไฟล์)
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Current Gallery Images -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-images"></i> รูปภาพปัจจุบัน ({{ $activity->images->count() }} รูป)
                            </h5>
                        </div>
                        <div class="card-body">
                            @if($activity->images && $activity->images->count() > 0)
                                <div class="row" id="galleryGrid">
                                    @foreach($activity->images as $image)
                                        <div class="col-lg-3 col-md-4 col-6 mb-3" data-image-id="{{ $image->id }}">
                                            <div class="gallery-item position-relative">
                                                <img src="{{ $image->image_url }}"
                                                     class="img-thumbnail w-100" style="height: 200px; object-fit: cover; cursor: pointer;"
                                                     alt="Gallery Image" onclick="viewImage(this.src)">
                                                
                                                <div class="position-absolute top-0 end-0 m-2">
                                                    <button type="button" class="btn btn-danger btn-sm" 
                                                            onclick="deleteGalleryImage({{ $image->id }})"
                                                            title="ลบรูปภาพนี้">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                                
                                                <div class="position-absolute bottom-0 start-0 end-0 bg-dark bg-opacity-75 text-white p-2">
                                                    <small>รูปที่ {{ $image->sort_order }}</small>
                                                    @if($image->caption)
                                                        <br><small>{{ $image->caption }}</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center text-muted py-5">
                                    <i class="fas fa-images fa-4x mb-3"></i>
                                    <h5>ยังไม่มีรูปภาพในแกลเลอรี่</h5>
                                    <p class="mb-0">เริ่มต้นด้วยการอัพโหลดรูปภาพแรกของคุณ</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection

@push('styles')
<style>
.gallery-item {
    transition: transform 0.2s ease-in-out;
}
.gallery-item:hover {
    transform: scale(1.02);
}
.gallery-item img {
    border-radius: 8px;
}
#imagePreview .preview-item {
    position: relative;
}
#imagePreview .preview-item img {
    border-radius: 8px;
}
.btn-remove-preview {
    position: absolute;
    top: 5px;
    right: 5px;
    padding: 2px 6px;
    font-size: 10px;
    border-radius: 50%;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // Success message
    @if(session('success'))
        Swal.fire({
            icon: 'success',
            title: 'สำเร็จ!',
            text: '{{ session('success') }}',
            timer: 3000,
            showConfirmButton: false
        });
    @endif

    // Image preview functionality
    $('#gallery_images').on('change', function() {
        const files = this.files;
        const previewContainer = $('#imagePreview');
        const uploadBtn = $('#uploadBtn');
        const uploadHelp = $('#uploadHelp');

        previewContainer.empty();

        if (files.length > 0) {
            uploadBtn.show();
            uploadHelp.hide();
            $('#fileCount').text(files.length);

            Array.from(files).forEach((file, index) => {
                if (!file.type.startsWith('image/')) {
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    const imageHtml = `
                        <div class="col-lg-2 col-md-3 col-4 mb-2">
                            <div class="preview-item">
                                <img src="${e.target.result}" class="img-thumbnail w-100"
                                     style="height: 120px; object-fit: cover;"
                                     alt="Preview">
                                <button type="button" class="btn btn-danger btn-sm btn-remove-preview"
                                        onclick="removePreviewItem(this, ${index})"
                                        title="ลบรูปภาพนี้">
                                    <i class="fas fa-times"></i>
                                </button>
                                <div class="position-absolute bottom-0 start-0 m-1">
                                    <small class="badge bg-primary">${index + 1}</small>
                                </div>
                            </div>
                        </div>
                    `;
                    previewContainer.append(imageHtml);
                };
                reader.readAsDataURL(file);
            });
        } else {
            uploadBtn.hide();
            uploadHelp.show();
            $('#fileCount').text(0);
        }
    });
});

function removePreviewItem(button, index) {
    $(button).closest('.col-lg-2').remove();
    
    // Update file count
    const remainingItems = $('#imagePreview .col-lg-2').length;
    $('#fileCount').text(remainingItems);
    
    if (remainingItems === 0) {
        $('#uploadBtn').hide();
        $('#uploadHelp').show();
    }
}

function deleteGalleryImage(imageId) {
    Swal.fire({
        title: 'ลบรูปภาพ?',
        text: 'คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '<i class="fas fa-trash"></i> ลบเลย',
        cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `{{ url('admin/activities/' . $activity->id . '/images') }}/${imageId}`,
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        $(`[data-image-id="${imageId}"]`).fadeOut(function() {
                            $(this).remove();
                            updateImageCount();
                        });
                        Swal.fire({
                            icon: 'success',
                            title: 'ลบสำเร็จ!',
                            text: 'ลบรูปภาพเรียบร้อยแล้ว',
                            timer: 2000,
                            showConfirmButton: false
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'เกิดข้อผิดพลาด!',
                            text: response.message || 'ไม่สามารถลบรูปภาพได้'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'เกิดข้อผิดพลาด!',
                        text: 'ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้'
                    });
                }
            });
        }
    });
}

function updateImageCount() {
    const currentCount = $('#galleryGrid [data-image-id]').length;
    $('.card-header h5').html(`<i class="fas fa-images"></i> รูปภาพปัจจุบัน (${currentCount} รูป)`);
}

function viewImage(src) {
    Swal.fire({
        imageUrl: src,
        imageAlt: 'Gallery Image',
        showCloseButton: true,
        showConfirmButton: false,
        width: 'auto',
        padding: '1rem',
        background: '#fff',
        customClass: {
            image: 'img-fluid'
        }
    });
}
</script>
@endpush
