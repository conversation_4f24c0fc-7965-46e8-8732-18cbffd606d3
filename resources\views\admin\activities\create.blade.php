@extends('layouts.admin')

@section('title', 'เพิ่มกิจกรรมใหม่ - Admin Panel')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-plus me-2 text-success"></i>เพิ่มกิจกรรมใหม่
                    </h1>
                    <p class="text-muted">เพิ่มกิจกรรมใหม่ให้กับเว็บไซต์</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.dashboard') }}">
                                <i class="fas fa-home"></i> แดชบอร์ด
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.activities.index') }}">
                                <i class="fas fa-calendar-alt"></i> จัดการกิจกรรม
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            <i class="fas fa-plus"></i> เพิ่มกิจกรรมใหม่
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-plus"></i> เพิ่มกิจกรรมใหม่
                            </h5>
                        </div>
                        
                        <form action="{{ route('admin.activities.store') }}" method="POST" enctype="multipart/form-data" id="activityForm">
                            @csrf
                            
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">ชื่อกิจกรรม <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                                   id="title" name="title" value="{{ old('title') }}" required>
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="description" class="form-label">รายละเอียดกิจกรรม <span class="text-danger">*</span></label>
                                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                                      id="description" name="description" rows="5" required>{{ old('description') }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="category_id" class="form-label">หมวดหมู่</label>
                                                    <select class="form-control @error('category_id') is-invalid @enderror" id="category_id" name="category_id">
                                                        <option value="">เลือกหมวดหมู่</option>
                                                        @foreach($categories ?? [] as $category)
                                                            <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                                                {{ $category->name }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    @error('category_id')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="activity_date" class="form-label">วันที่จัดกิจกรรม</label>
                                                    <input type="date" class="form-control @error('activity_date') is-invalid @enderror" 
                                                           id="activity_date" name="activity_date" value="{{ old('activity_date') }}">
                                                    @error('activity_date')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="location" class="form-label">สถานที่</label>
                                            <input type="text" class="form-control @error('location') is-invalid @enderror" 
                                                   id="location" name="location" value="{{ old('location') }}">
                                            @error('location')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="is_published" name="is_published" value="1"
                                                       {{ old('is_published') ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_published">
                                                    เผยแพร่กิจกรรมทันที
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="cover_image" class="form-label">รูปภาพหน้าปก</label>
                                            <input type="file" class="form-control @error('cover_image') is-invalid @enderror"
                                                   id="cover_image" name="cover_image" accept="image/*">
                                            <small class="form-text text-muted">รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 2MB)</small>
                                            @error('cover_image')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="text-center">
                                            <label class="form-label">ตัวอย่างรูปภาพ</label>
                                            <div id="imagePreview" style="display: none;">
                                                <img id="previewImg" src="" class="img-thumbnail"
                                                     style="max-width: 100%; max-height: 200px;" alt="ตัวอย่างรูปภาพ">
                                                <div class="mt-2">
                                                    <button type="button" class="btn btn-sm btn-danger" id="removePreview">
                                                        <i class="fas fa-trash"></i> ลบรูปภาพ
                                                    </button>
                                                </div>
                                            </div>
                                            <div id="noImageDisplay" class="border border-dashed rounded p-4 text-muted">
                                                <i class="fas fa-image fa-3x mb-2"></i>
                                                <p class="mb-0">เลือกรูปภาพหน้าปก</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Gallery Images Section -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <hr>
                                        <h5 class="mb-3">
                                            <i class="fas fa-images text-success"></i> รูปภาพแกลเลอรี่ (ไม่บังคับ)
                                        </h5>

                                        <div class="mb-3">
                                            <label for="gallery_images" class="form-label">เลือกรูปภาพแกลเลอรี่</label>
                                            <input type="file" class="form-control @error('gallery_images') is-invalid @enderror"
                                                   id="gallery_images" name="gallery_images[]" accept="image/*" multiple>
                                            <small class="form-text text-muted">สามารถเลือกหลายไฟล์พร้อมกัน (JPG, PNG, GIF - ขนาดไม่เกิน 2MB ต่อไฟล์)</small>
                                            @error('gallery_images')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div id="galleryPreview" class="row mb-3">
                                            <!-- Preview images will appear here -->
                                        </div>

                                        <div id="galleryHelp" class="text-center text-muted mb-3">
                                            <i class="fas fa-images fa-2x mb-2"></i>
                                            <p class="mb-0">เลือกรูปภาพเพื่อดู preview ก่อนบันทึก</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card-footer bg-light">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('admin.activities.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> กลับ
                                    </a>
                                    <div>
                                        <button type="reset" class="btn btn-outline-secondary me-2">
                                            <i class="fas fa-undo"></i> รีเซ็ต
                                        </button>
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-save"></i> บันทึกกิจกรรม
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection

@push('styles')
<style>
.card {
    transition: transform 0.2s ease-in-out;
}
.form-control:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40,167,69,.25);
}
#imagePreview img {
    border-radius: 8px;
}
#noImageDisplay {
    transition: all 0.3s ease;
}
#noImageDisplay:hover {
    border-color: #28a745;
    background-color: #f8f9fa;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // Image preview functionality
    $('#cover_image').on('change', function() {
        let file = this.files[0];
        if (file) {
            // Validate file type
            if (!file.type.startsWith('image/')) {
                Swal.fire({
                    icon: 'error',
                    title: 'ไฟล์ไม่ถูกต้อง',
                    text: 'กรุณาเลือกไฟล์รูปภาพเท่านั้น'
                });
                this.value = '';
                return;
            }

            // Validate file size (2MB)
            if (file.size > 2 * 1024 * 1024) {
                Swal.fire({
                    icon: 'error',
                    title: 'ไฟล์ใหญ่เกินไป',
                    text: 'กรุณาเลือกไฟล์ที่มีขนาดไม่เกิน 2MB'
                });
                this.value = '';
                return;
            }

            let reader = new FileReader();
            reader.onload = function(e) {
                $('#previewImg').attr('src', e.target.result);
                $('#imagePreview').show();
                $('#noImageDisplay').hide();
            };
            reader.readAsDataURL(file);
        }
    });

    // Remove preview
    $('#removePreview').on('click', function() {
        $('#cover_image').val('');
        $('#imagePreview').hide();
        $('#noImageDisplay').show();
    });

    // Form validation
    $('#activityForm').on('submit', function(e) {
        let title = $('#title').val().trim();
        let description = $('#description').val().trim();

        if (!title) {
            e.preventDefault();
            Swal.fire({
                icon: 'error',
                title: 'กรุณากรอกข้อมูล',
                text: 'กรุณากรอกชื่อกิจกรรม'
            });
            $('#title').focus();
            return false;
        }

        if (!description) {
            e.preventDefault();
            Swal.fire({
                icon: 'error',
                title: 'กรุณากรอกข้อมูล',
                text: 'กรุณากรอกรายละเอียดกิจกรรม'
            });
            $('#description').focus();
            return false;
        }
    });

    // Gallery images preview
    $('#gallery_images').on('change', function() {
        const files = this.files;
        const previewContainer = $('#galleryPreview');
        const galleryHelp = $('#galleryHelp');

        previewContainer.empty();

        if (files.length > 0) {
            galleryHelp.hide();

            Array.from(files).forEach((file, index) => {
                if (!file.type.startsWith('image/')) {
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    const imageHtml = `
                        <div class="col-lg-2 col-md-3 col-4 mb-2">
                            <div class="position-relative gallery-preview-item">
                                <img src="${e.target.result}" class="img-thumbnail w-100"
                                     style="height: 120px; object-fit: cover; border-radius: 8px;"
                                     alt="Preview">
                                <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0 m-1"
                                        style="padding: 2px 6px; font-size: 10px; border-radius: 50%;"
                                        onclick="removeGalleryPreviewItem(this, ${index})"
                                        title="ลบรูปภาพนี้">
                                    <i class="fas fa-times"></i>
                                </button>
                                <div class="position-absolute bottom-0 start-0 m-1">
                                    <small class="badge bg-success">${index + 1}</small>
                                </div>
                            </div>
                        </div>
                    `;
                    previewContainer.append(imageHtml);
                };
                reader.readAsDataURL(file);
            });
        } else {
            galleryHelp.show();
        }
    });

    // Reset form
    $('button[type="reset"]').on('click', function() {
        $('#imagePreview').hide();
        $('#noImageDisplay').show();
        $('#galleryPreview').empty();
        $('#galleryHelp').show();
    });
});

function removeGalleryPreviewItem(button, index) {
    $(button).closest('.col-lg-2').remove();

    // Update numbering for remaining items
    $('.gallery-preview-item').each(function(i) {
        $(this).find('.badge').text(i + 1);
    });

    // Show help if no items left
    if ($('.gallery-preview-item').length === 0) {
        $('#galleryHelp').show();
    }
}
</script>
@endpush
