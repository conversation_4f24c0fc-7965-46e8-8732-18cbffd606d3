/**
 * Admin Activities Edit Page JavaScript
 * Handles all interactions for editing activities
 */

class ActivityEditManager {
    constructor() {
        this.currentImageId = null;
        this.csrfToken = null;
        this.activityId = null;
        this.init();
    }

    init() {
        console.log('ActivityEditManager initialized');
        
        // Check for required dependencies
        if (typeof bootstrap === 'undefined') {
            console.error('Bootstrap is required but not loaded');
            this.showError('Bootstrap library is not loaded');
            return;
        }

        // Get CSRF token
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        if (!csrfMeta) {
            console.error('CSRF token not found');
            this.showError('CSRF token not found. Please refresh the page.');
            return;
        }
        this.csrfToken = csrfMeta.getAttribute('content');

        // Get activity ID
        this.activityId = this.getActivityId();
        if (!this.activityId) {
            console.error('Activity ID not found');
            this.showError('ไม่พบข้อมูลกิจกรรม กรุณาโหลดหน้าใหม่');
            return;
        }

        // Initialize all components
        this.initializeEventListeners();
        this.initializeSortable();
        this.initializeImagePreviews();
    }

    initializeEventListeners() {
        // Edit caption buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.edit-caption-btn')) {
                this.handleEditCaption(e);
            }
        });

        // Replace image buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.replace-image-btn')) {
                this.handleReplaceImage(e);
            }
        });

        // Delete image buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.delete-image')) {
                this.handleDeleteImage(e);
            }
        });

        // Save caption button
        const saveCaptionBtn = document.getElementById('saveCaptionBtn');
        if (saveCaptionBtn) {
            saveCaptionBtn.addEventListener('click', () => this.saveCaption());
        }

        // Replace image button
        const replaceImageBtn = document.getElementById('replaceImageBtn');
        if (replaceImageBtn) {
            replaceImageBtn.addEventListener('click', () => this.replaceImage());
        }

        // Modal close handlers
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-close') || e.target.getAttribute('data-bs-dismiss') === 'modal') {
                const modal = e.target.closest('.modal');
                if (modal) {
                    this.closeModal(modal.id);
                }
            }
        });

        // ESC key handler
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModals = document.querySelectorAll('.modal.show');
                openModals.forEach(modal => this.closeModal(modal.id));
            }
        });

        // Add gallery image button
        const addGalleryBtn = document.getElementById('add_gallery_image');
        if (addGalleryBtn) {
            addGalleryBtn.addEventListener('click', () => this.addGalleryItem());
        }

        // File input changes
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('custom-file-input')) {
                this.updateFileLabel(e.target);
            }

            if (e.target.classList.contains('gallery-image') || e.target.id === 'cover_image') {
                this.handleImagePreview(e.target);
            }

            if (e.target.id === 'newImage') {
                this.handleReplaceImagePreview(e.target);
            }
        });

        // Remove gallery item buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.remove-gallery-item')) {
                this.removeGalleryItem(e.target);
            }
        });
    }

    initializeSortable() {
        const sortableGallery = document.getElementById('sortable-gallery');
        if (sortableGallery && typeof Sortable !== 'undefined') {
            new Sortable(sortableGallery, {
                animation: 150,
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                handle: '.drag-handle',
                onEnd: (evt) => this.updateGalleryOrder(evt)
            });
        }
    }

    initializeImagePreviews() {
        // Cover image preview
        const coverInput = document.getElementById('cover_image');
        if (coverInput) {
            coverInput.addEventListener('change', (e) => this.handleCoverImagePreview(e));
        }

        // Gallery image previews
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('gallery-image')) {
                this.handleGalleryImagePreview(e);
            }
        });

        // Replace image preview
        const newImageInput = document.getElementById('newImage');
        if (newImageInput) {
            newImageInput.addEventListener('change', (e) => this.handleReplaceImagePreview(e));
        }
    }

    handleEditCaption(e) {
        e.preventDefault();
        e.stopPropagation();

        const button = e.target.closest('.edit-caption-btn');
        this.currentImageId = button.getAttribute('data-image-id');
        const currentCaption = button.getAttribute('data-current-caption') || '';

        console.log('Edit caption for image:', this.currentImageId);

        document.getElementById('editCaption').value = currentCaption;
        this.showModal('editCaptionModal');
    }

    handleReplaceImage(e) {
        const button = e.target.closest('.replace-image-btn');
        this.currentImageId = button.getAttribute('data-image-id');

        // Get current caption
        const editBtn = document.querySelector(`.edit-caption-btn[data-image-id="${this.currentImageId}"]`);
        const currentCaption = editBtn ? editBtn.getAttribute('data-current-caption') || '' : '';
        
        document.getElementById('replaceCaption').value = currentCaption;
        this.showModal('replaceImageModal');
    }

    handleDeleteImage(e) {
        const button = e.target.closest('.delete-image');
        const imageId = button.getAttribute('data-image-id');

        if (!imageId) {
            this.showError('ไม่พบข้อมูลรูปภาพ');
            return;
        }

        if (confirm('คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้? การดำเนินการนี้ไม่สามารถยกเลิกได้')) {
            this.deleteImage(imageId, button);
        }
    }

    async saveCaption() {
        const saveBtn = document.getElementById('saveCaptionBtn');
        const newCaption = document.getElementById('editCaption').value;

        if (!this.currentImageId) {
            this.showError('ไม่พบข้อมูลรูปภาพ');
            return;
        }

        this.showLoading(saveBtn);

        try {
            const response = await fetch(this.getRoute('admin.activities.images.update-caption', this.currentImageId), {
                method: 'PUT',
                headers: {
                    'X-CSRF-TOKEN': this.csrfToken,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ caption: newCaption })
            });

            const data = await this.handleResponse(response);
            
            if (data.success) {
                this.updateCaptionInUI(this.currentImageId, newCaption);
                this.closeModal('editCaptionModal');
                this.showSuccess('อัปเดตคำบรรยายสำเร็จ!');
            } else {
                this.showError(data.message || 'เกิดข้อผิดพลาดในการบันทึกคำบรรยาย');
            }
        } catch (error) {
            console.error('Error saving caption:', error);
            this.showError('เกิดข้อผิดพลาดในการบันทึกคำบรรยาย: ' + error.message);
        } finally {
            this.hideLoading(saveBtn);
        }
    }

    async replaceImage() {
        const replaceBtn = document.getElementById('replaceImageBtn');
        const fileInput = document.getElementById('newImage');
        const caption = document.getElementById('replaceCaption').value;

        if (!this.currentImageId) {
            this.showError('ไม่พบข้อมูลรูปภาพ');
            return;
        }

        if (!fileInput.files[0]) {
            this.showError('กรุณาเลือกรูปภาพใหม่');
            return;
        }

        this.showLoading(replaceBtn);

        try {
            const formData = new FormData();
            formData.append('new_image', fileInput.files[0]);
            formData.append('caption', caption);

            const response = await fetch(this.getRoute('admin.activities.images.replace', this.currentImageId), {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': this.csrfToken,
                },
                body: formData
            });

            const data = await this.handleResponse(response);
            
            if (data.success) {
                this.updateImageInUI(this.currentImageId, data.new_image_url, caption);
                this.closeModal('replaceImageModal');
                this.resetReplaceForm();
                this.showSuccess('เปลี่ยนรูปภาพสำเร็จ!');
            } else {
                this.showError(data.message || 'เกิดข้อผิดพลาดในการเปลี่ยนรูปภาพ');
            }
        } catch (error) {
            console.error('Error replacing image:', error);
            this.showError('เกิดข้อผิดพลาดในการเปลี่ยนรูปภาพ: ' + error.message);
        } finally {
            this.hideLoading(replaceBtn);
        }
    }

    async deleteImage(imageId, button) {
        console.log('Deleting image:', imageId, 'Activity ID:', this.activityId);

        if (!this.activityId) {
            this.showError('ไม่พบข้อมูลกิจกรรม');
            return;
        }

        if (!imageId) {
            this.showError('ไม่พบข้อมูลรูปภาพ');
            return;
        }

        this.showLoading(button);

        try {
            const url = this.getRoute('admin.activities.images.delete', imageId);
            console.log('Delete URL:', url);

            const response = await fetch(url, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': this.csrfToken,
                    'Content-Type': 'application/json',
                },
            });

            console.log('Response status:', response.status);

            const data = await this.handleResponse(response);
            console.log('Response data:', data);

            if (data.success) {
                this.removeImageFromUI(button);
                this.showSuccess('ลบรูปภาพสำเร็จ!');
            } else {
                this.showError(data.message || 'เกิดข้อผิดพลาดในการลบรูปภาพ');
            }
        } catch (error) {
            console.error('Error deleting image:', error);
            this.showError('เกิดข้อผิดพลาดในการลบรูปภาพ: ' + error.message);
        } finally {
            this.hideLoading(button);
        }
    }

    // Utility methods
    getActivityId() {
        // Try multiple ways to get activity ID
        if (window.activityId) {
            return window.activityId;
        }

        // Try to get from URL
        const pathParts = window.location.pathname.split('/');
        const activitiesIndex = pathParts.indexOf('activities');
        if (activitiesIndex !== -1 && pathParts[activitiesIndex + 1]) {
            return pathParts[activitiesIndex + 1];
        }

        // Try to get from form action
        const form = document.getElementById('activityForm');
        if (form && form.action) {
            const actionParts = form.action.split('/');
            const activitiesIndex = actionParts.indexOf('activities');
            if (activitiesIndex !== -1 && actionParts[activitiesIndex + 1]) {
                return actionParts[activitiesIndex + 1];
            }
        }

        return null;
    }

    getRoute(routeName, id) {
        const routes = {
            'admin.activities.images.update-caption': `/admin/activities/images/${id}/caption`,
            'admin.activities.images.replace': `/admin/activities/images/${id}/replace`,
            'admin.activities.images.delete': `/admin/activities/${this.activityId}/images/${id}`,
        };
        return routes[routeName];
    }

    async handleResponse(response) {
        const contentType = response.headers.get('content-type');

        if (!response.ok) {
            let errorMessage = `HTTP error! status: ${response.status}`;

            if (contentType && contentType.includes('application/json')) {
                try {
                    const errorData = await response.json();
                    errorMessage = errorData.message || errorMessage;
                } catch (e) {
                    console.error('Error parsing error response:', e);
                }
            } else {
                try {
                    const errorText = await response.text();
                    console.error('Non-JSON error response:', errorText);
                } catch (e) {
                    console.error('Error reading error response:', e);
                }
            }

            throw new Error(errorMessage);
        }

        if (contentType && contentType.includes('application/json')) {
            return await response.json();
        } else {
            throw new Error('Response is not JSON');
        }
    }

    updateCaptionInUI(imageId, caption) {
        const captionDisplay = document.querySelector(`.caption-display[data-image-id="${imageId}"] p`);
        if (captionDisplay) {
            if (caption.trim()) {
                captionDisplay.textContent = caption;
                captionDisplay.classList.remove('text-muted');
            } else {
                captionDisplay.textContent = 'ไม่มีคำบรรยาย';
                captionDisplay.classList.add('text-muted');
            }
        }

        const editBtn = document.querySelector(`.edit-caption-btn[data-image-id="${imageId}"]`);
        if (editBtn) {
            editBtn.setAttribute('data-current-caption', caption);
        }
    }

    updateImageInUI(imageId, newImageUrl, caption) {
        const galleryImage = document.querySelector(`.gallery-image[data-image-id="${imageId}"]`);
        if (galleryImage && newImageUrl) {
            galleryImage.src = newImageUrl;
        }
        this.updateCaptionInUI(imageId, caption);
    }

    removeImageFromUI(button) {
        const container = button.closest('.gallery-item-container');
        if (container) {
            container.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            container.style.opacity = '0';
            container.style.transform = 'scale(0.8)';
            setTimeout(() => {
                container.remove();
            }, 300);
        }
    }

    resetReplaceForm() {
        document.getElementById('replaceImageForm').reset();
        document.getElementById('replacePreview').style.display = 'none';
        document.getElementById('replace_file_info').style.display = 'none';
        document.querySelector('label[for="newImage"]').textContent = 'เลือกรูปภาพ...';
    }

    showModal(modalId) {
        try {
            const modal = document.getElementById(modalId);
            if (modal) {
                const bsModal = new bootstrap.Modal(modal);
                bsModal.show();
            } else {
                throw new Error('Modal element not found');
            }
        } catch (error) {
            console.error('Error opening modal:', error);
            this.showError('เกิดข้อผิดพลาดในการเปิด Modal');
        }
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return;

        const bsModal = bootstrap.Modal.getInstance(modal);
        if (bsModal) {
            bsModal.hide();
        } else {
            const newModal = new bootstrap.Modal(modal);
            newModal.hide();
        }
    }

    showLoading(element) {
        if (element) {
            element.disabled = true;
            const originalText = element.innerHTML;
            element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> กำลังดำเนินการ...';
            element.dataset.originalText = originalText;
        }
    }

    hideLoading(element) {
        if (element && element.dataset.originalText) {
            element.disabled = false;
            element.innerHTML = element.dataset.originalText;
            delete element.dataset.originalText;
        }
    }

    showSuccess(message) {
        this.showToast(message, 'success');
    }

    showError(message) {
        this.showToast(message, 'error');
    }

    showToast(message, type = 'success') {
        // Remove existing toasts
        const existingToast = document.querySelector(`.${type}-toast`);
        if (existingToast) {
            existingToast.remove();
        }

        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';

        const toast = document.createElement('div');
        toast.className = `alert ${alertClass} alert-dismissible fade show position-fixed ${type}-toast`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);';
        toast.innerHTML = `
            <i class="fas ${iconClass} me-2"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        document.body.appendChild(toast);

        // Auto remove
        setTimeout(() => {
            if (toast.parentNode) {
                toast.style.opacity = '0';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }
        }, type === 'success' ? 4000 : 5000);

        // Manual close
        const closeBtn = toast.querySelector('.btn-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                toast.style.opacity = '0';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            });
        }
    }

    // ฟังก์ชันสำหรับแปลงขนาดไฟล์
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // ฟังก์ชันสำหรับเพิ่มรายการแกลเลอรี่
    addGalleryItem() {
        const container = document.getElementById('gallery_container');
        if (!container) return;

        const newItem = document.createElement('div');
        newItem.className = 'gallery-item mb-3';
        newItem.innerHTML = `
            <div class="row">
                <div class="col-md-8">
                    <div class="custom-file">
                        <input type="file"
                               name="gallery_images[]"
                               class="custom-file-input gallery-image"
                               accept="image/*">
                        <label class="custom-file-label">เลือกรูปภาพ...</label>
                    </div>
                </div>
                <div class="col-md-3">
                    <input type="text"
                           name="captions[]"
                           class="form-control"
                           placeholder="คำบรรยายรูปภาพ (ไม่บังคับ)">
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-danger btn-sm remove-gallery-item">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="image-preview mt-2" style="display: none;">
                <img src="" class="img-thumbnail" style="max-width: 150px;">
            </div>
        `;
        container.appendChild(newItem);
    }

    // ฟังก์ชันสำหรับลบรายการแกลเลอรี่
    removeGalleryItem(button) {
        const galleryItem = button.closest('.gallery-item');
        if (galleryItem) {
            galleryItem.remove();
        }
    }

    // ฟังก์ชันสำหรับอัปเดต label ของ file input
    updateFileLabel(input) {
        const fileName = input.files[0] ? input.files[0].name : 'เลือกรูปภาพ...';
        const label = input.nextElementSibling;
        if (label) {
            label.textContent = fileName;
        }
    }

    // ฟังก์ชันสำหรับจัดการ preview รูปภาพ
    handleImagePreview(input) {
        const file = input.files[0];
        if (!file) return;

        // ตรวจสอบประเภทไฟล์
        if (!this.isValidImageFile(file)) {
            this.showError('กรุณาเลือกไฟล์รูปภาพที่ถูกต้อง (JPEG, JPG, PNG, GIF, WebP)');
            input.value = '';
            return;
        }

        // ตรวจสอบขนาดไฟล์ (2MB)
        if (file.size > 2 * 1024 * 1024) {
            this.showError('ขนาดไฟล์ต้องไม่เกิน 2MB');
            input.value = '';
            return;
        }

        // แสดง preview
        this.showImagePreview(input, file);
    }

    // ตรวจสอบประเภทไฟล์รูปภาพ
    isValidImageFile(file) {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        return validTypes.includes(file.type);
    }

    // แสดง preview รูปภาพ
    showImagePreview(input, file) {
        const reader = new FileReader();

        reader.onload = (e) => {
            let preview;

            if (input.id === 'cover_image') {
                preview = document.getElementById('cover_preview');
                const img = document.getElementById('cover_preview_img');
                if (img) {
                    img.src = e.target.result;
                    preview.style.display = 'block';
                }
            } else if (input.classList.contains('gallery-image')) {
                preview = input.closest('.gallery-item').querySelector('.image-preview');
                const img = preview.querySelector('img');
                if (img) {
                    img.src = e.target.result;
                    preview.style.display = 'block';
                }
            }
        };

        reader.readAsDataURL(file);
    }

    // จัดการ preview สำหรับการเปลี่ยนรูปภาพ
    handleReplaceImagePreview(input) {
        const file = input.files[0];
        const preview = document.getElementById('replacePreview');
        const previewImg = document.getElementById('replacePreviewImg');
        const label = document.querySelector('label[for="newImage"]');

        if (file) {
            // ตรวจสอบประเภทไฟล์
            if (!this.isValidImageFile(file)) {
                this.showError('กรุณาเลือกไฟล์รูปภาพที่ถูกต้อง (JPEG, JPG, PNG, GIF, WebP)');
                input.value = '';
                return;
            }

            // ตรวจสอบขนาดไฟล์ (2MB)
            if (file.size > 2 * 1024 * 1024) {
                this.showError('ขนาดไฟล์ต้องไม่เกิน 2MB');
                input.value = '';
                return;
            }

            // แสดงชื่อไฟล์ใน label
            if (label) {
                label.textContent = file.name;
            }

            // แสดงตัวอย่างรูปภาพ
            const reader = new FileReader();
            reader.onload = function(e) {
                if (previewImg) {
                    previewImg.src = e.target.result;
                    preview.style.display = 'block';
                }
            };
            reader.readAsDataURL(file);
        } else {
            if (label) {
                label.textContent = 'เลือกรูปภาพ...';
            }
            if (preview) {
                preview.style.display = 'none';
            }
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.activityEditManager = new ActivityEditManager();
});
