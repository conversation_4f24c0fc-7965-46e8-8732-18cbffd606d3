<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Activity;
use App\Models\ActivityCategory;
use App\Models\ActivityImage;
use App\Helpers\ImageHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ActivityController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $activities = Activity::with(['category', 'images'])->latest()->get();
        return view('admin.activities.index', compact('activities'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = ActivityCategory::active()->get();

        // ถ้าไม่มีหมวดหมู่ ให้สร้างหมวดหมู่เริ่มต้น
        if ($categories->isEmpty()) {
            ActivityCategory::create([
                'name' => 'งานบุญประจำปี',
                'description' => 'งานบุญประจำปีของวัด',
                'color' => '#007bff',
                'is_active' => true,
            ]);

            ActivityCategory::create([
                'name' => 'งานพิเศษ',
                'description' => 'งานพิเศษต่างๆ',
                'color' => '#28a745',
                'is_active' => true,
            ]);

            ActivityCategory::create([
                'name' => 'กิจกรรมชุมชน',
                'description' => 'กิจกรรมเพื่อชุมชน',
                'color' => '#ffc107',
                'is_active' => true,
            ]);

            $categories = ActivityCategory::active()->get();
        }

        return view('admin.activities.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $data = $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'required|string',
                'category_id' => 'required|exists:activity_categories,id',
                'activity_date' => 'required|date',
                'location' => 'nullable|string|max:255',
                'cover_image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
                'gallery_images.*' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
            ]);

            $activityData = $request->only(['title', 'description', 'category_id', 'activity_date', 'location']);
            $activityData['is_published'] = $request->boolean('is_published');

            // Handle cover image upload
            if ($request->hasFile('cover_image')) {
                $activityData['cover_image'] = ImageHelper::uploadAndResize($request->file('cover_image'), 'activities');
            }

            $activity = Activity::create($activityData);

            // Handle gallery images
            $uploadedCount = 0;
            if ($request->hasFile('gallery_images')) {
                foreach ($request->file('gallery_images') as $index => $image) {
                    if ($image && $image->isValid()) {
                        try {
                            $imagePath = ImageHelper::uploadAndResize($image, 'activities/gallery');
                            ActivityImage::create([
                                'activity_id' => $activity->id,
                                'image_path' => $imagePath,
                                'caption' => null,
                                'sort_order' => $index + 1,
                            ]);
                            $uploadedCount++;
                        } catch (\Exception $e) {
                            \Log::error('Error uploading gallery image: ' . $e->getMessage());
                        }
                    }
                }
            }

            // Prepare success message
            $successMessage = 'เพิ่มกิจกรรมสำเร็จ';
            if ($uploadedCount > 0) {
                $successMessage .= " พร้อมรูปแกลเลอรี่ {$uploadedCount} รูป";
            }

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => $successMessage,
                    'activity' => $activity->load(['category', 'images'])
                ]);
            }

            return redirect()->route('admin.activities.index')->with('success', $successMessage);
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()
                ], 500);
            }

            return back()->withInput()->withErrors(['error' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Activity $activity)
    {
        $activity->load(['category', 'images']);

        if (request()->ajax()) {
            return response()->json([
                'id' => $activity->id,
                'title' => $activity->title,
                'description' => $activity->description,
                'category_id' => $activity->category_id,
                'activity_date' => $activity->activity_date ? $activity->activity_date->format('Y-m-d') : null,
                'location' => $activity->location,
                'is_published' => $activity->is_published,
                'cover_image_url' => $activity->cover_image_url,
                'cover_image_filename' => $activity->cover_image,
                'images' => $activity->images->map(function($image) {
                    return [
                        'id' => $image->id,
                        'image_url' => $image->image_url,
                        'caption' => $image->caption,
                        'sort_order' => $image->sort_order,
                        'filename' => $image->image_path
                    ];
                })
            ]);
        }

        return view('admin.activities.show', compact('activity'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Activity $activity)
    {
        $categories = ActivityCategory::active()->get();
        $activity->load(['category', 'images']);
        return view('admin.activities.edit', compact('activity', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Activity $activity)
    {
        try {
            $data = $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'required|string',
                'category_id' => 'nullable|exists:activity_categories,id',
                'activity_date' => 'nullable|date',
                'location' => 'nullable|string|max:255',
                'cover_image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
                'gallery_images.*' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
                'remove_cover_image' => 'nullable|boolean',
            ]);

            $activityData = $request->only(['title', 'description', 'category_id', 'activity_date', 'location']);
            $activityData['is_published'] = $request->boolean('is_published');

            // Handle cover image removal
            if ($request->input('remove_cover_image') == '1') {
                // Delete old cover image if exists
                if ($activity->cover_image && Storage::disk('public')->exists('activities/' . $activity->cover_image)) {
                    Storage::disk('public')->delete('activities/' . $activity->cover_image);
                }
                // Force remove image
                $activity->cover_image = null;
                $activity->save();
            }
            // Handle cover image upload
            elseif ($request->hasFile('cover_image')) {
                // Delete old cover image if exists
                if ($activity->cover_image && Storage::disk('public')->exists('activities/' . $activity->cover_image)) {
                    Storage::disk('public')->delete('activities/' . $activity->cover_image);
                }
                $activityData['cover_image'] = ImageHelper::uploadAndResize($request->file('cover_image'), 'activities');
            }

            $activity->update($activityData);

            // Handle new gallery images
            if ($request->hasFile('gallery_images')) {
                $maxSortOrder = $activity->images()->max('sort_order') ?? 0;
                foreach ($request->file('gallery_images') as $index => $image) {
                    if ($image && $image->isValid()) {
                        $imagePath = ImageHelper::uploadAndResize($image, 'activities/gallery');
                        ActivityImage::create([
                            'activity_id' => $activity->id,
                            'image_path' => $imagePath,
                            'caption' => null,
                            'sort_order' => $maxSortOrder + $index + 1,
                        ]);
                    }
                }
            }

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'อัปเดตกิจกรรมสำเร็จ',
                    'activity' => $activity->fresh()->load(['category', 'images'])
                ]);
            }

            return redirect()->route('admin.activities.edit', $activity)->with('success', 'อัปเดตกิจกรรมสำเร็จ');
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()
                ], 500);
            }

            return back()->withInput()->withErrors(['error' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Activity $activity)
    {
        try {
            // Delete cover image
            if ($activity->cover_image) {
                ImageHelper::deleteImage('activities/' . $activity->cover_image);
            }

            // Delete gallery images
            foreach ($activity->images as $image) {
                ImageHelper::deleteImage('activities/gallery/' . $image->image_path);
                $image->delete();
            }

            $activity->delete();

            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'ลบกิจกรรมสำเร็จ'
                ]);
            }

            return redirect()->route('admin.activities.index')->with('success', 'ลบกิจกรรมสำเร็จ');
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'เกิดข้อผิดพลาดในการลบกิจกรรม'
                ], 500);
            }

            return back()->withErrors(['error' => 'เกิดข้อผิดพลาดในการลบกิจกรรม']);
        }
    }

    /**
     * Update cover image only
     */
    public function updateCoverImage(Request $request, Activity $activity)
    {
        try {
            $request->validate([
                'cover_image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
                'remove_cover_image' => 'nullable|boolean',
            ]);

            // Handle cover image removal
            if ($request->input('remove_cover_image') == '1') {
                // Delete old cover image if exists
                if ($activity->cover_image) {
                    ImageHelper::deleteImage('activities/' . $activity->cover_image);
                }
                $activity->cover_image = null;
                $activity->save();

                return redirect()->back()->with('success', 'ลบรูปหน้าปกเรียบร้อยแล้ว');
            }

            // Handle cover image upload
            if ($request->hasFile('cover_image')) {
                // Delete old cover image if exists
                if ($activity->cover_image) {
                    ImageHelper::deleteImage('activities/' . $activity->cover_image);
                }

                $imagePath = ImageHelper::uploadAndResize($request->file('cover_image'), 'activities');
                $activity->cover_image = $imagePath;
                $activity->save();

                return redirect()->back()->with('success', 'อัพเดตรูปหน้าปกเรียบร้อยแล้ว');
            }

            return redirect()->back()->with('error', 'กรุณาเลือกรูปภาพที่ต้องการอัพโหลด');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'เกิดข้อผิดพลาดในการอัพเดตรูปหน้าปก');
        }
    }

    /**
     * Show gallery management page
     */
    public function gallery(Activity $activity)
    {
        $activity->load('images');
        return view('admin.activities.gallery', compact('activity'));
    }

    /**
     * Upload gallery images
     */
    public function uploadGalleryImages(Request $request, Activity $activity)
    {
        try {
            $request->validate([
                'gallery_images.*' => 'required|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
            ]);

            if (!$request->hasFile('gallery_images')) {
                return redirect()->back()->with('error', 'กรุณาเลือกรูปภาพที่ต้องการอัพโหลด');
            }

            $uploadedCount = 0;
            $maxSortOrder = $activity->images()->max('sort_order') ?? 0;

            foreach ($request->file('gallery_images') as $index => $image) {
                if ($image && $image->isValid()) {
                    try {
                        $imagePath = ImageHelper::uploadAndResize($image, 'activities/gallery');
                        ActivityImage::create([
                            'activity_id' => $activity->id,
                            'image_path' => $imagePath,
                            'caption' => null,
                            'sort_order' => $maxSortOrder + $index + 1,
                        ]);
                        $uploadedCount++;
                    } catch (\Exception $e) {
                        \Log::error('Error uploading gallery image: ' . $e->getMessage());
                    }
                }
            }

            if ($uploadedCount > 0) {
                return redirect()->back()->with('success', "อัพโหลดรูปภาพสำเร็จ {$uploadedCount} รูป");
            } else {
                return redirect()->back()->with('error', 'ไม่สามารถอัพโหลดรูปภาพได้');
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ');
        }
    }

    /**
     * Delete a gallery image
     */
    public function deleteImage(Activity $activity, ActivityImage $image)
    {
        try {
            \Log::info('Deleting image', [
                'activity_id' => $activity->id,
                'image_id' => $image->id,
                'image_path' => $image->image_path
            ]);

            // Check if image belongs to this activity
            if ($image->activity_id !== $activity->id) {
                \Log::warning('Image does not belong to activity', [
                    'image_activity_id' => $image->activity_id,
                    'requested_activity_id' => $activity->id
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'ไม่พบรูปภาพที่ต้องการลบ'
                ], 404);
            }

            // Delete image file
            $imagePath = 'activities/gallery/' . $image->image_path;
            if ($image->image_path && Storage::disk('public')->exists($imagePath)) {
                Storage::disk('public')->delete($imagePath);
                \Log::info('Image file deleted', ['path' => $imagePath]);
            } else {
                \Log::warning('Image file not found', ['path' => $imagePath]);
            }

            // Delete database record
            $image->delete();
            \Log::info('Image record deleted from database', ['image_id' => $image->id]);

            return response()->json([
                'success' => true,
                'message' => 'ลบรูปภาพสำเร็จ'
            ]);
        } catch (\Exception $e) {
            \Log::error('Error deleting image', [
                'error' => $e->getMessage(),
                'activity_id' => $activity->id ?? null,
                'image_id' => $image->id ?? null
            ]);

            return response()->json([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการลบรูปภาพ: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle publish status
     */
    public function togglePublish(Request $request, Activity $activity)
    {
        try {
            $activity->update([
                'is_published' => $request->is_published
            ]);

            return response()->json([
                'success' => true,
                'message' => $request->is_published ? 'เผยแพร่กิจกรรมแล้ว' : 'ยกเลิกการเผยแพร่กิจกรรมแล้ว'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการเปลี่ยนสถานะ'
            ], 500);
        }
    }

    /**
     * Bulk delete activities
     */
    public function bulkDelete(Request $request)
    {
        try {
            $activityIds = $request->ids; // แก้ไขจาก activity_ids เป็น ids

            if (empty($activityIds)) {
                return response()->json([
                    'success' => false,
                    'message' => 'กรุณาเลือกกิจกรรมที่ต้องการลบ'
                ], 400);
            }

            $activities = Activity::whereIn('id', $activityIds)->get();

            foreach ($activities as $activity) {
                // Delete cover image
                if ($activity->cover_image) {
                    ImageHelper::deleteImage('activities/' . $activity->cover_image);
                }

                // Delete gallery images
                foreach ($activity->images as $image) {
                    ImageHelper::deleteImage('activities/gallery/' . $image->image_path);
                    $image->delete();
                }

                $activity->delete();
            }

            return response()->json([
                'success' => true,
                'message' => 'ลบกิจกรรมที่เลือกสำเร็จ (' . count($activities) . ' รายการ)'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการลบกิจกรรม'
            ], 500);
        }
    }

    /**
     * Delete multiple images from activity gallery
     */
    public function deleteMultipleImages(Request $request, Activity $activity)
    {
        try {
            $imageIds = $request->input('image_ids', []);

            if (empty($imageIds)) {
                return redirect()->back()->with('error', 'ไม่มีรูปภาพที่เลือก');
            }

            $deletedCount = 0;
            foreach ($imageIds as $imageId) {
                $image = ActivityImage::where('activity_id', $activity->id)
                                    ->where('id', $imageId)
                                    ->first();

                if ($image) {
                    // Delete physical file
                    ImageHelper::deleteImage($image->image_path);

                    // Delete database record
                    $image->delete();
                    $deletedCount++;
                }
            }

            return redirect()->back()->with('success', "ลบรูปภาพสำเร็จ {$deletedCount} รูป");

        } catch (\Exception $e) {
            \Log::error('Error deleting multiple images: ' . $e->getMessage());
            return redirect()->back()->with('error', 'เกิดข้อผิดพลาดในการลบรูปภาพ');
        }
    }
}
